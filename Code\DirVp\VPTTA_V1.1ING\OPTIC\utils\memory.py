import torch
import numpy as np
from numpy.linalg import norm


class Memory(object):
    """
        Create the empty memory buffer
    """

    def __init__(self, size, dimension=512):
        self.memory = {}
        self.size = size
        self.dimension = dimension

    def reset(self):
        self.memory = {}

    def get_size(self):
        return len(self.memory)

    def push(self, keys, anchor_positions, anchor_params):
        """存储语义摘要、锚点位置和参数
        Args:
            keys: [B, dimension] 语义摘要
            anchor_positions: [N, 2] 锚点位置
            anchor_params: [N, 7] 锚点参数
        """
        for i, key in enumerate(keys):
            if len(self.memory.keys()) > self.size:
                self.memory.pop(list(self.memory)[0])

            # 存储格式：(anchor_positions, anchor_params)
            self.memory.update({
                key.reshape(self.dimension).tobytes(): (anchor_positions, anchor_params)
            })

    def get_neighbours(self, keys, query_anchors, k):
        """语义检索和空间匹配
        Args:
            keys: [B, dimension] 查询的语义摘要
            query_anchors: [N, 2] 查询图像的锚点位置
            k: int 检索的邻居数量
        Returns:
            matched_params: [N, 7] 匹配的参数
            similarity_score: float 平均相似度分数
        """
        if len(self.memory.keys()) < k:
            return None, None

        # 语义检索
        semantic_key = keys[0]  # 只处理第一个batch
        retrieved_data, similarity_scores = self._semantic_retrieval(semantic_key, k)

        # 空间位置匹配
        matched_params = self._spatial_parameter_matching(query_anchors, retrieved_data)

        return matched_params, np.mean(similarity_scores)

    def _semantic_retrieval(self, semantic_key, k):
        """基于语义相似度检索
        Args:
            semantic_key: [dimension] 语义摘要
            k: int 检索数量
        Returns:
            retrieved_data: list of (anchor_positions, anchor_params)
            similarity_scores: [k] 相似度分数
        """
        self.all_keys = np.array([
            np.frombuffer(key, dtype=np.float32).reshape(self.dimension)
            for key in self.memory.keys()
        ])

        # 计算相似度
        similarity_scores = np.dot(self.all_keys, semantic_key.T) / (
            norm(self.all_keys, axis=1) * norm(semantic_key.T)
        )

        # 获取top-k邻居
        top_k_indices = np.argpartition(similarity_scores, -k)[-k:]
        K_neighbour_keys = self.all_keys[top_k_indices]

        # 检索对应的数据
        retrieved_data = [self.memory[nkey.tobytes()] for nkey in K_neighbour_keys]

        return retrieved_data, similarity_scores[top_k_indices]

    def _spatial_parameter_matching(self, query_anchors, retrieved_data):
        """基于空间位置匹配参数
        Args:
            query_anchors: [N, 2] 查询锚点位置
            retrieved_data: list of (anchor_positions, anchor_params)
        Returns:
            matched_params: [N, 7] 匹配的参数
        """
        N = len(query_anchors)
        matched_params = np.zeros((N, 7))

        for i, query_anchor in enumerate(query_anchors):
            best_param = None
            min_distance = float('inf')

            # 在所有检索到的数据中找最近的锚点
            for anchor_positions, anchor_params in retrieved_data:
                if len(anchor_positions) > 0:
                    # 计算距离
                    distances = np.linalg.norm(anchor_positions - query_anchor, axis=1)
                    min_idx = np.argmin(distances)

                    if distances[min_idx] < min_distance:
                        min_distance = distances[min_idx]
                        best_param = anchor_params[min_idx]

            # 如果找到匹配的参数，使用它；否则使用默认值
            if best_param is not None:
                matched_params[i] = best_param
            else:
                # 默认参数
                matched_params[i] = [0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 1.0]

        return matched_params
