import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torchvision.transforms.functional import gaussian_blur


class SemanticAnchorPrompt(nn.Module):
    def __init__(self, num_anchors=25, image_size=512, uncertainty_threshold=0.4, edge_weight=0.6, device='cuda:0'):
        super().__init__()
        self.num_anchors = num_anchors
        self.image_size = image_size
        self.uncertainty_threshold = uncertainty_threshold
        self.edge_weight = edge_weight
        self.device = device
        
        # 动态锚点坐标，初始化为空，在extract_semantic_anchors中设置
        self.anchor_coords = None
        
        # 控制点参数初始化
        # 位移参数 Δp: [num_anchors, 2]
        self.delta_p = nn.Parameter(torch.randn(self.num_anchors, 2) * 1.0)
        
        # 外观调整参数 Δa: [num_anchors, 3] 
        self.delta_a = nn.Parameter(torch.randn(self.num_anchors, 3) * 0.1)
        
        # 影响半径参数 r: [num_anchors]
        self.radius = nn.Parameter(torch.empty(self.num_anchors).uniform_(0.3, 0.8))

        # 显著性权重参数 α: [num_anchors]
        self.alpha = nn.Parameter(torch.empty(self.num_anchors).uniform_(0.8, 1.2))

        # 软阈值陡峭度参数
        self.sigmoid_steepness = 10.0
        
    def extract_semantic_anchors(self, probability_map):
        """提取语义锚点
        Args:
            probability_map: [B, C, H, W] 模型预测的概率图
        """
        B, C, H, W = probability_map.shape
        
        # 对于多类分割，使用最大概率类别的不确定性
        if C > 1:
            prob_map = torch.max(probability_map, dim=1)[0]  # [B, H, W]
        else:
            prob_map = probability_map.squeeze(1)  # [B, H, W]
        
        # 只处理第一个batch
        prob_map = prob_map[0]  # [H, W]
        
        # 提取不确定性锚点
        uncertainty_anchors = self._extract_uncertainty_anchors(prob_map)
        
        # 提取边缘锚点
        edge_anchors = self._extract_edge_anchors(prob_map)
        
        # 合并候选锚点
        all_candidates = torch.cat([uncertainty_anchors, edge_anchors], dim=0)
        
        # 选择代表性锚点
        selected_anchors = self._select_representative_anchors(all_candidates)
        
        # 更新锚点坐标
        self.anchor_coords = selected_anchors.to(self.device)
        
    def _extract_uncertainty_anchors(self, prob_map):
        """提取不确定性锚点：概率接近0.5的像素点
        Args:
            prob_map: [H, W] 概率图
        Returns:
            anchors: [N, 2] 不确定性锚点坐标 (归一化到[0,1])
        """
        H, W = prob_map.shape
        
        # 计算不确定性：距离0.5的距离
        uncertainty = 1.0 - 2.0 * torch.abs(prob_map - 0.5)
        
        # 找到不确定性高于阈值的点
        uncertain_mask = uncertainty > self.uncertainty_threshold
        
        # 获取坐标
        y_coords, x_coords = torch.where(uncertain_mask)
        
        if len(y_coords) == 0:
            # 如果没有找到不确定点，返回随机点
            num_points = self.num_anchors // 2
            y_coords = torch.randint(0, H, (num_points,))
            x_coords = torch.randint(0, W, (num_points,))
        
        # 归一化坐标到[0,1]
        anchors = torch.stack([
            x_coords.float() / (W - 1),
            y_coords.float() / (H - 1)
        ], dim=1)
        
        return anchors
        
    def _extract_edge_anchors(self, prob_map):
        """提取边缘锚点：使用Sobel算子检测边缘
        Args:
            prob_map: [H, W] 概率图
        Returns:
            anchors: [N, 2] 边缘锚点坐标 (归一化到[0,1])
        """
        H, W = prob_map.shape
        
        # Sobel算子
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32, device=prob_map.device)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32, device=prob_map.device)
        
        # 添加batch和channel维度进行卷积
        prob_map_4d = prob_map.unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
        sobel_x_4d = sobel_x.unsqueeze(0).unsqueeze(0)    # [1, 1, 3, 3]
        sobel_y_4d = sobel_y.unsqueeze(0).unsqueeze(0)    # [1, 1, 3, 3]
        
        # 计算梯度
        grad_x = F.conv2d(prob_map_4d, sobel_x_4d, padding=1).squeeze()  # [H, W]
        grad_y = F.conv2d(prob_map_4d, sobel_y_4d, padding=1).squeeze()  # [H, W]
        
        # 计算梯度幅值
        edge_magnitude = torch.sqrt(grad_x**2 + grad_y**2)
        
        # 找到边缘强度高的点
        edge_threshold = torch.quantile(edge_magnitude, 0.8)  # 取前20%的边缘点
        edge_mask = edge_magnitude > edge_threshold
        
        # 获取坐标
        y_coords, x_coords = torch.where(edge_mask)
        
        if len(y_coords) == 0:
            # 如果没有找到边缘点，返回随机点
            num_points = self.num_anchors // 2
            y_coords = torch.randint(0, H, (num_points,))
            x_coords = torch.randint(0, W, (num_points,))
        
        # 归一化坐标到[0,1]
        anchors = torch.stack([
            x_coords.float() / (W - 1),
            y_coords.float() / (H - 1)
        ], dim=1)
        
        return anchors
        
    def _select_representative_anchors(self, candidates):
        """使用NMS选择代表性锚点
        Args:
            candidates: [N, 2] 候选锚点坐标
        Returns:
            selected: [num_anchors, 2] 选择的锚点坐标
        """
        if len(candidates) <= self.num_anchors:
            # 如果候选点不够，用随机点补充
            num_missing = self.num_anchors - len(candidates)
            random_points = torch.rand(num_missing, 2, device=candidates.device)
            return torch.cat([candidates, random_points], dim=0)
        
        # 简单的均匀采样策略
        indices = torch.linspace(0, len(candidates) - 1, self.num_anchors, dtype=torch.long)
        selected = candidates[indices]
        
        return selected

    def update(self, init_data):
        """使用Memory Bank检索到的数据进行参数初始化
        Args:
            init_data: [num_anchors, 7] 初始化参数数据
        """
        with torch.no_grad():
            if init_data is not None and len(init_data) > 0:
                # 确保在正确的设备上
                init_data = torch.tensor(init_data, device=self.device, dtype=torch.float32)

                # 如果数据维度不匹配，进行调整
                if init_data.shape[0] != self.num_anchors:
                    # 重复或截断以匹配锚点数量
                    if init_data.shape[0] < self.num_anchors:
                        # 重复数据
                        repeat_times = (self.num_anchors + init_data.shape[0] - 1) // init_data.shape[0]
                        init_data = init_data.repeat(repeat_times, 1)[:self.num_anchors]
                    else:
                        # 截断数据
                        init_data = init_data[:self.num_anchors]

                # 更新参数
                self.delta_p.copy_(init_data[:, :2])      # 位移参数
                self.delta_a.copy_(init_data[:, 2:5])     # 外观参数
                self.radius.copy_(init_data[:, 5])        # 影响半径参数
                self.alpha.copy_(init_data[:, 6])         # 显著性权重参数

    def _generate_dense_field(self, image_shape):
        """生成稠密变换场
        Args:
            image_shape: (H, W)
        Returns:
            displacement_field: [H, W, 2] 位移场
            appearance_field: [H, W, 3] 外观场
        """
        if self.anchor_coords is None:
            raise ValueError("Anchor coordinates not set. Call extract_semantic_anchors first.")

        H, W = image_shape

        # 生成像素坐标网格 [H*W, 2]
        y_coords = torch.linspace(0, 1, H, device=self.device)
        x_coords = torch.linspace(0, 1, W, device=self.device)
        grid_y, grid_x = torch.meshgrid(y_coords, x_coords, indexing='ij')
        pixel_coords = torch.stack([grid_x.flatten(), grid_y.flatten()], dim=1)

        # 插值生成位移场
        displacement_flat = self._idw_interpolation(
            pixel_coords, self.anchor_coords, self.delta_p, self.radius, self.alpha
        )
        displacement_field = displacement_flat.view(H, W, 2)

        # 插值生成外观场
        appearance_flat = self._idw_interpolation(
            pixel_coords, self.anchor_coords, self.delta_a, self.radius, self.alpha
        )
        appearance_field = appearance_flat.view(H, W, 3)

        return displacement_field, appearance_field

    def _idw_interpolation(self, pixel_coords, anchor_coords, anchor_values, radius, alpha):
        """反距离加权插值
        Args:
            pixel_coords: [N_pixels, 2] 像素坐标
            anchor_coords: [N_anchors, 2] 锚点坐标
            anchor_values: [N_anchors, D] 锚点值
            radius: [N_anchors] 影响半径
            alpha: [N_anchors] 显著性权重
        Returns:
            interpolated: [N_pixels, D] 插值结果
        """
        # 计算距离 [N_pixels, N_anchors]
        distances = torch.cdist(pixel_coords, anchor_coords)

        # 计算权重
        weights = torch.zeros_like(distances)
        for i in range(len(anchor_coords)):
            # 软阈值函数
            normalized_dist = distances[:, i] / (radius[i] + 1e-8)
            soft_threshold = torch.sigmoid(-self.sigmoid_steepness * (normalized_dist - 1.0))
            weights[:, i] = alpha[i] * soft_threshold / (distances[:, i] + 1e-8)

        # 归一化权重
        weight_sum = weights.sum(dim=1, keepdim=True) + 1e-8
        weights = weights / weight_sum

        # 插值
        interpolated = torch.matmul(weights, anchor_values)

        return interpolated

    def _apply_geometric_transform(self, x, displacement_field):
        """应用几何变换
        Args:
            x: [B, C, H, W] 输入图像
            displacement_field: [H, W, 2] 位移场
        Returns:
            transformed: [B, C, H, W] 变换后图像
        """
        B, C, H, W = x.shape

        # 创建采样网格
        grid_y, grid_x = torch.meshgrid(
            torch.linspace(-1, 1, H, device=self.device),
            torch.linspace(-1, 1, W, device=self.device),
            indexing='ij'
        )
        base_grid = torch.stack([grid_x, grid_y], dim=2)  # [H, W, 2]

        # 将位移场从[0,1]坐标系转换到[-1,1]坐标系
        displacement_normalized = displacement_field * 2.0  # [H, W, 2]

        # 应用位移
        sampling_grid = base_grid + displacement_normalized  # [H, W, 2]
        sampling_grid = sampling_grid.unsqueeze(0).repeat(B, 1, 1, 1)  # [B, H, W, 2]

        # 双线性插值采样
        transformed = F.grid_sample(x, sampling_grid, mode='bilinear', padding_mode='border', align_corners=True)

        return transformed

    def _apply_appearance_transform(self, x, appearance_field):
        """应用外观变换
        Args:
            x: [B, C, H, W] 输入图像
            appearance_field: [H, W, 3] 外观场
        Returns:
            transformed: [B, C, H, W] 变换后图像
        """
        B, C, H, W = x.shape

        # 扩展外观场到batch维度
        appearance_field = appearance_field.unsqueeze(0).repeat(B, 1, 1, 1)  # [B, H, W, 3]
        appearance_field = appearance_field.permute(0, 3, 1, 2)  # [B, 3, H, W]

        # 确保通道数匹配
        if C == 3:
            # RGB图像，直接应用
            transformed = x + appearance_field
        else:
            # 其他通道数，只应用前C个通道
            transformed = x + appearance_field[:, :C]

        # 限制像素值范围
        transformed = torch.clamp(transformed, 0, 1)

        return transformed

    def forward(self, x, model):
        """前向传播
        Args:
            x: [B, 3, H, W] 输入图像
            model: ResUnet模型（用于生成图像摘要）
        Returns:
            transformed_image: [B, 3, H, W] 变换后图像
            image_summary: [B, feature_dim] 图像摘要
        """
        B, C, H, W = x.shape

        # 生成稠密变换场
        displacement_field, appearance_field = self._generate_dense_field((H, W))

        # 应用几何变换
        transformed_image = self._apply_geometric_transform(x, displacement_field)

        # 应用外观变换
        transformed_image = self._apply_appearance_transform(transformed_image, appearance_field)

        # 生成图像摘要（使用原始图像）
        from .semantic_image_summary import SemanticImageSummary
        summary_generator = SemanticImageSummary()
        image_summary = summary_generator.generate_summary(x, model)

        return transformed_image, image_summary
