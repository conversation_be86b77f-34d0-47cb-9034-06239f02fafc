import torch
import torch.nn as nn


class SemanticImageSummary:
    """语义图像摘要生成器：使用ResUnet编码器特征进行全局平均池化"""
    
    def __init__(self):
        pass
        
    def generate_summary(self, x, model):
        """使用ResUnet编码器生成语义摘要
        Args:
            x: [B, 3, H, W] 输入图像
            model: ResUnet模型
        Returns:
            summary: [B, feature_dim] 语义摘要向量
        """
        with torch.no_grad():
            # 提取编码器特征
            encoder_features = self._extract_encoder_features(x, model)
            
            # 全局平均池化
            semantic_summary = self._global_average_pooling(encoder_features)
            
        return semantic_summary
        
    def _extract_encoder_features(self, x, model):
        """提取ResUnet编码器的最后一层特征
        Args:
            x: [B, 3, H, W] 输入图像
            model: ResUnet模型
        Returns:
            features: [B, C, H', W'] 编码器特征图
        """
        # 使用ResUnet的编码器部分
        features, _ = model.res(x)  # 获取编码器输出
        return features
        
    def _global_average_pooling(self, features):
        """对特征图进行全局平均池化
        Args:
            features: [B, C, H, W] 特征图
        Returns:
            pooled: [B, C] 池化后的特征向量
        """
        # 全局平均池化
        pooled_features = torch.mean(features, dim=(2, 3))  # [B, C]
        return pooled_features
        
    def get_summary_dimension(self):
        """返回摘要向量的维度
        根据ResNet backbone确定：
        - ResNet34: 512维
        - ResNet50: 2048维
        """
        # 默认返回ResNet34的特征维度，实际维度在运行时确定
        return 512
